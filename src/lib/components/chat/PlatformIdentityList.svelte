<script lang="ts">
	import { createEventDispatcher, onMount, onDestroy } from 'svelte';
	import LoadingSpinner from '../common/LoadingSpinner.svelte';
	import InfiniteScroll from '../common/InfiniteScroll.svelte';
	import FilterPanel from './FilterPanel.svelte';
	import type { CustomerPlatformIdentity, Message } from '$lib/types/customer';
	import { getBackendUrl } from '$src/lib/config';
	import { platformWebSocket } from '$lib/websocket/platformWebSocket';
	import { conversationStore } from '$lib/stores/conversationStore';
	import { t, language } from '$lib/stores/i18n';
	import { get } from 'svelte/store';
	import { AdjustmentsHorizontalSolid, ArrowUpDownOutline } from 'flowbite-svelte-icons';
	import { Tabs, TabItem } from 'flowbite-svelte';

	export let platformIdentities: CustomerPlatformIdentity[] = [];
	export let selectedPlatformId: number | null = null;
	export let hasMore: boolean = false;

	const dispatch = createEventDispatcher();

	let searchTerm = '';
	let latestMessages: Map<number, Message> = new Map();
	let unreadCounts: Map<number, number> = new Map();
	let loadingMore = false;

	// Sort and filter state
	let sortBy = 'recent'; // 'recent', 'name', 'platform'
	let sortDirection = 'desc'; // 'asc', 'desc'
	let showFilterDropdown = false;
	let showFilterPanel = false;
	let filterData = {
		dateRange: null,
		customStartDate: null,
		customEndDate: null,
		platforms: new Set(['All']),
		channels: new Set(['All']),
		unreadFilter: new Set(['All']),
		statuses: new Set(['All']),
		tags: new Set(['All']),
		searchText: ''
	};

	$: filteredIdentities = filterIdentities(platformIdentities, searchTerm, filterData);
	$: sortedIdentities = sortIdentities(filteredIdentities, latestMessages);

	onMount(() => {
		loadAdditionalData();

		// Only access window in browser environment
		if (typeof window !== 'undefined') {
			// Listen for WebSocket events
			window.addEventListener('platform-new-message', handleNewMessage);
			window.addEventListener('platform-status-update', handleStatusUpdate);
			window.addEventListener('platform-typing', handleTypingIndicator);
			window.addEventListener('platform-bulk-update', handleBulkUpdate);

			// Subscribe to all visible platforms for real-time updates
			const visiblePlatformIds = platformIdentities.map((p) => p.id);
			if (visiblePlatformIds.length > 0) {
				platformWebSocket.subscribeToMultiplePlatforms(visiblePlatformIds);
			}
		}
	});

	onDestroy(() => {
		// Only access window in browser environment
		if (typeof window !== 'undefined') {
			// Clean up event listeners
			window.removeEventListener('platform-new-message', handleNewMessage);
			window.removeEventListener('platform-status-update', handleStatusUpdate);
			window.removeEventListener('platform-typing', handleTypingIndicator);
			window.removeEventListener('platform-bulk-update', handleBulkUpdate);
		}
	});

	async function loadAdditionalData() {
		// Load latest messages and unread counts for all platform identities
		const platformIds = platformIdentities.map((p) => p.id);
		if (platformIds.length > 0) {
			await Promise.all([loadLatestMessages(platformIds), loadUnreadCounts(platformIds)]);
		}
	}

	async function loadLatestMessages(platformIds: number[]) {
		try {
			// Batch load latest messages
			const response = await fetch(
				`${getBackendUrl()}/customer/api/platform-messages/?platform_ids=${platformIds.join(',')}`,
				{ credentials: 'include' }
			);

			if (response.ok) {
				const data = await response.json();
				latestMessages = new Map(Object.entries(data).map(([k, v]) => [parseInt(k), v as Message]));
			}
		} catch (error) {
			console.error('Error loading latest messages:', error);
		}
	}

	async function loadUnreadCounts(platformIds: number[]) {
		try {
			// Batch load unread counts
			const response = await fetch(
				`${getBackendUrl()}/customer/api/platform-unread-counts/?platform_ids=${platformIds.join(',')}`,
				{ credentials: 'include' }
			);

			if (response.ok) {
				const data = await response.json();
				unreadCounts = new Map(Object.entries(data).map(([k, v]) => [parseInt(k), v as number]));
			}
		} catch (error) {
			console.error('Error loading unread counts:', error);
		}
	}

	function filterIdentities(identities: CustomerPlatformIdentity[], search: string, filters: any) {
		let filtered = identities;

		// Apply search filter from main search input
		if (search) {
			const searchLower = search.toLowerCase();
			filtered = filtered.filter(
				(p) =>
					// Search in platform username (if available)
					p.platform_username?.toLowerCase().includes(searchLower)
				// Search in display name
				// p.display_name?.toLowerCase().includes(searchLower) ||
				// Search in platform user ID
				// p.platform_user_id?.toLowerCase().includes(searchLower) ||
				// Search in channel name
				// p.channel_name?.toLowerCase().includes(searchLower) ||
				// Search in platform type
				// p.platform.toLowerCase().includes(searchLower) ||
				// Search in customer name (if customer is an object)
				// (typeof p.customer === 'object' &&
				// 	p.customer !== null &&
				// 	(p.customer as any).name?.toLowerCase().includes(searchLower)) ||
				// Search in customer email (if customer is an object)
				// (typeof p.customer === 'object' &&
				// 	p.customer !== null &&
				// 	(p.customer as any).email?.toLowerCase().includes(searchLower))
			);
		}

		// Apply platform filter
		if (filters.platforms && !filters.platforms.has('All')) {
			filtered = filtered.filter((p) => filters.platforms.has(p.platform));
		}

		// Apply channel filter
		if (filters.channels && !filters.channels.has('All')) {
			filtered = filtered.filter((p) => {
				const channelName = p.channel_name || 'No Channel';
				return filters.channels.has(channelName);
			});
		}

		// Apply unread messages filter
		if (filters.unreadFilter && !filters.unreadFilter.has('All')) {
			filtered = filtered.filter((p) => {
				const unreadCount = unreadCounts.get(p.id) || p.unread_count || 0;

				if (filters.unreadFilter.has('unread')) {
					return unreadCount > 0;
				}
				return true;
			});
		}

		// Apply date range filter
		if (filters.dateRange) {
			const now = new Date();
			let startDate: Date;

			switch (filters.dateRange) {
				case 'today':
					startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
					break;
				case 'week':
					startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
					break;
				case 'month':
					startDate = new Date(now.getFullYear(), now.getMonth(), 1);
					break;
				case 'last-3-months':
					startDate = new Date(now.getFullYear(), now.getMonth() - 3, 1);
					break;
				case 'last-6-months':
					startDate = new Date(now.getFullYear(), now.getMonth() - 6, 1);
					break;
				case 'custom':
					if (filters.customStartDate) {
						startDate = new Date(filters.customStartDate);
					}
					break;
			}

			if (startDate) {
				filtered = filtered.filter((p) => {
					const messageTime =
						latestMessages.get(p.id)?.created_on || p.last_interaction || p.created_on;
					return messageTime && new Date(messageTime) >= startDate;
				});
			}

			// Apply custom end date if specified
			if (filters.dateRange === 'custom' && filters.customEndDate) {
				const endDate = new Date(filters.customEndDate);
				endDate.setHours(23, 59, 59, 999); // End of day
				filtered = filtered.filter((p) => {
					const messageTime =
						latestMessages.get(p.id)?.created_on || p.last_interaction || p.created_on;
					return messageTime && new Date(messageTime) <= endDate;
				});
			}
		}

		// Apply status filter (placeholder logic - you can customize based on your data structure)
		if (filters.status && !filters.status.has('All')) {
			// This is placeholder logic - adjust based on how you determine if a conversation is active/archived
			filtered = filtered.filter((p) => {
				if (filters.status.has('Active')) {
					// Consider active if there's recent activity or unread messages
					const hasRecentActivity =
						latestMessages.has(p.id) || (p.unread_count && p.unread_count > 0);
					return hasRecentActivity;
				}
				if (filters.status.has('Archived')) {
					// Consider archived if no recent activity
					const hasRecentActivity =
						latestMessages.has(p.id) || (p.unread_count && p.unread_count > 0);
					return !hasRecentActivity;
				}
				return true;
			});
		}

		return filtered;
	}

	function sortIdentities(identities: CustomerPlatformIdentity[], messages: Map<number, Message>) {
		return [...identities].sort((a, b) => {
			const aMsg = messages.get(a.id);
			const bMsg = messages.get(b.id);

			// If both have messages, sort by message time
			if (aMsg && bMsg) {
				return new Date(bMsg.created_on).getTime() - new Date(aMsg.created_on).getTime();
			}

			// If only one has a message, it goes first
			if (aMsg && !bMsg) return -1;
			if (!aMsg && bMsg) return 1;

			// If neither has messages, sort by last interaction
			const aTime = a.last_interaction || a.created_on;
			const bTime = b.last_interaction || b.created_on;
			return new Date(bTime).getTime() - new Date(aTime).getTime();
		});
	}

	function handleIdentityClick(identity: CustomerPlatformIdentity) {
		// Check if customer is just an ID or a full object
		let customerId: number;

		if (typeof identity.customer === 'number') {
			// If customer is just an ID, use it directly
			customerId = identity.customer;
		} else if (
			identity.customer &&
			typeof identity.customer === 'object' &&
			identity.customer.customer_id
		) {
			// If customer is an object with customer_id
			customerId = identity.customer.customer_id;
		} else {
			console.error('Platform identity missing valid customer data:', identity);
			return;
		}

		dispatch('select', {
			platformId: identity.id,
			customerId: customerId
		});
	}

	function handleLoadMore() {
		if (!loadingMore && hasMore) {
			loadingMore = true;
			dispatch('loadMore');
			loadingMore = false;
		}
	}

	// function handleNewMessage(event: CustomEvent) {
	//     const { platformId, message, unreadCount } = event.detail;
	//     // TODO - Delete this console log in production
	//     console.log('New message event:', event.detail);

	//     // Update latest message
	//     latestMessages.set(platformId, message);
	//     latestMessages = latestMessages; // Trigger reactivity

	//     // Update unread count
	//     unreadCounts.set(platformId, unreadCount);
	//     unreadCounts = unreadCounts; // Trigger reactivity

	//     // IMPORTANT: Add the message to conversation store
	//     // This ensures the message appears in the conversation view
	//     conversationStore.addMessage(platformId, message);

	//     // Re-sort the list to move updated platform to top
	//     sortedIdentities = sortIdentities(filteredIdentities, latestMessages);
	// }

	function handleNewMessage(event: CustomEvent) {
		const { platformId, message, unreadCount } = event.detail;
		// TODO - Delete this console log in production
		console.log('New message event:', event.detail);

		// Update latest message
		latestMessages.set(platformId, message);
		latestMessages = latestMessages; // Trigger reactivity

		// Update unread count
		unreadCounts.set(platformId, unreadCount);
		unreadCounts = unreadCounts; // Trigger reactivity

		// IMPORTANT: Add the message to conversation store
		// This ensures the message appears in the conversation view
		conversationStore.addMessage(platformId, message);

		// Re-sort the list to move updated platform to top
		sortedIdentities = sortIdentities(filteredIdentities, latestMessages);
	}

	function handleStatusUpdate(event: CustomEvent) {
		const { platformId, status } = event.detail;

		// Update platform status if needed
		const platform = platformIdentities.find((p) => p.id === platformId);
		if (platform) {
			// You can add status to your platform type if needed
			// platform.status = status;
			platformIdentities = platformIdentities; // Trigger reactivity
		}
	}

	function handleTypingIndicator(event: CustomEvent) {
		const { platformId, isTyping, userName } = event.detail;
		// You can implement typing indicator UI here if needed
	}

	function handleBulkUpdate(event: CustomEvent) {
		const updates = event.detail;

		// Update multiple platforms at once
		Object.entries(updates).forEach(([platformId, data]: [string, any]) => {
			const id = parseInt(platformId);

			if (data.latest_message) {
				latestMessages.set(id, data.latest_message);
			}

			if (data.unread_count !== undefined) {
				unreadCounts.set(id, data.unread_count);
			}
		});

		// Trigger reactivity
		latestMessages = latestMessages;
		unreadCounts = unreadCounts;
		sortedIdentities = sortIdentities(filteredIdentities, latestMessages);
	}

	function formatTime(dateString: string): string {
		const lang = get(language); // 'en' or 'th'
		const date = new Date(dateString);
		const now = new Date();
		const diff = now.getTime() - date.getTime();

		// Define localized labels
		const labels = {
			en: {
				justNow: 'Just now',
				minutesAgo: (m: number) => `${m}m ago`,
				hoursAgo: (h: number) => `${h}h ago`,
				daysAgo: (d: number) => `${d}d ago`,
				yesterday: 'Yesterday'
			},
			th: {
				justNow: 'เมื่อสักครู่',
				minutesAgo: (m: number) => `${m} นาทีที่แล้ว`,
				hoursAgo: (h: number) => `${h} ชั่วโมงที่แล้ว`,
				daysAgo: (d: number) => `${d} วันที่แล้ว`,
				yesterday: 'เมื่อวานนี้'
			}
		};

		// Fallback to 'en' if lang is not supported
		const currentLang = ['en', 'th'].includes(lang) ? lang : 'en';
		const l = labels[currentLang as 'en' | 'th'];

		if (diff < 60000) return l.justNow;
		if (diff < 3600000) return l.minutesAgo(Math.floor(diff / 60000));
		if (diff > 86400000 && diff < 172800000) return l.yesterday;
		if (diff < 86400000) return l.hoursAgo(Math.floor(diff / 3600000));
		if (diff < 604800000) return l.daysAgo(Math.floor(diff / 86400000));

		// // Less than a minute
		// if (diff < 60000) return 'Just now';

		// // Less than an hour
		// if (diff < 3600000) {
		//     const minutes = Math.floor(diff / 60000);
		//     return `${minutes}m ago`;
		// }

		// // Less than a day
		// if (diff < 86400000) {
		//     const hours = Math.floor(diff / 3600000);
		//     return `${hours}h ago`;
		// }

		// // Less than a week
		// if (diff < 604800000) {
		//     const days = Math.floor(diff / 86400000);
		//     return `${days}d ago`;
		// }

		// // Format as date
		// return date.toLocaleDateString();

		// Format as date in local language
		return date.toLocaleDateString(lang === 'th' ? 'th-TH' : 'en-US', {
			year: 'numeric',
			month: 'short',
			day: 'numeric'
		});
	}

	function getPlatformColor(platform: string): string {
		const colors = {
			LINE: 'bg-green-100 text-green-700',
			WHATSAPP: 'bg-green-100 text-green-700',
			FACEBOOK: 'bg-blue-100 text-blue-700',
			INSTAGRAM: 'bg-purple-100 text-purple-700',
			TELEGRAM: 'bg-sky-100 text-sky-700'
		};
		return colors[platform] || 'bg-gray-100 text-gray-700';
	}

	function getCustomerName(identity: CustomerPlatformIdentity): string {
		if (typeof identity.customer === 'object') {
			return identity.customer.name || identity.customer.email || 'Unknown Customer';
		} else {
			return `Customer #${identity.customer}`;
		}
	}

	// Sort and filter handlers
	function handleSort() {
		// Toggle sort direction if already sorting by recent, otherwise set to recent
		if (sortBy === 'recent') {
			sortDirection = sortDirection === 'desc' ? 'asc' : 'desc';
		} else {
			sortBy = 'recent';
			sortDirection = 'desc';
		}
		// Re-sort the identities
		sortedIdentities = sortIdentities(filteredIdentities, latestMessages);
	}

	function handleFilter(event: MouseEvent) {
		event.stopPropagation();
		// console.log('Filter button clicked! Current showFilterPanel:', showFilterPanel);
		showFilterPanel = !showFilterPanel;
		// console.log('New showFilterPanel value:', showFilterPanel);
	}

	function handleFilterApply(event: CustomEvent) {
		filterData = event.detail;
		// The reactive statement will automatically update filteredIdentities
	}

	function handleFilterClose() {
		showFilterPanel = false;
	}

	$: {
		// print each items in platformidentities
		console.log('platformIdentities:', platformIdentities);
		console.log('filteredIdentities:', filteredIdentities);
		console.log('sortedIdentities:', sortedIdentities);
	}
</script>

<div class="flex h-full flex-col">
	<!-- Header with Search -->
	<div class="border-b border-gray-200 p-4">
		<h2 class="mb-3 text-lg font-semibold">{t('chat_center')}</h2>
		<div class="flex items-center gap-1">
			<div class="relative flex-grow">
				<input
					type="text"
					bind:value={searchTerm}
					placeholder={t('search_platform')}
					class="w-full rounded-lg border border-gray-300 px-3 py-2 pl-9 text-sm text-gray-600
                           focus:outline-none focus:ring-2 focus:ring-blue-500"
				/>
				<svg
					class="absolute left-3 top-2.5 h-4 w-4 text-gray-400"
					fill="none"
					stroke="currentColor"
					viewBox="0 0 24 24"
				>
					<path
						stroke-linecap="round"
						stroke-linejoin="round"
						stroke-width="2"
						d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
					/>
				</svg>
			</div>
			<!-- <div class="flex-shrink-0">
				<button
					type="button"
					on:click={handleSort}
					class="flex cursor-pointer items-center justify-center gap-1 rounded-lg border border-gray-300 px-2 py-2 text-sm
                           transition-colors hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
					aria-label="Sort conversations"
				>
					<ArrowUpDownOutline class="h-5 w-5 text-gray-600" />
				</button>
			</div> -->
			<div class="relative flex-shrink-0">
				<button
					type="button"
					on:click={handleFilter}
					class="flex cursor-pointer items-center justify-center gap-1 rounded-lg border border-gray-300 px-2 py-2 text-sm
                           transition-colors hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500
                           {showFilterPanel ? 'border-blue-300 bg-blue-50' : ''}"
					aria-label="Filter conversations"
					aria-expanded={showFilterPanel}
				>
					<AdjustmentsHorizontalSolid class="h-5 w-5 text-gray-600" />
				</button>

				<FilterPanel
					bind:isOpen={showFilterPanel}
					bind:filterData
					{sortedIdentities}
					{unreadCounts}
					on:apply={handleFilterApply}
					on:close={handleFilterClose}
				/>
			</div>
		</div>
	</div>

	<!-- Platform Identity List -->
	<Tabs tabStyle="underline" defaultClass="flex justify-left" contentClass="m-0 p-0">
		<TabItem open title="Assigned">
			<div class="custom-scrollbar flex-1 overflow-y-auto">
				{#if sortedIdentities.length === 0}
					<div class="p-8 text-center text-gray-500">
						{searchTerm ? 'No conversations found' : 'No active conversations'}
					</div>
				{:else}
					<div class="divide-y divide-gray-100">
						{#each sortedIdentities as identity (identity.id)}
							<button
								class="relative w-full p-4 text-left transition-colors hover:bg-gray-50
									   {selectedPlatformId === identity.id ? 'border-l-4 border-blue-500 bg-blue-50' : ''}"
								on:click={() => handleIdentityClick(identity)}
							>
								<div class="flex items-start justify-between">
									<div class="min-w-0 flex-1 pr-2">
										<!-- Customer and Platform Info -->
										<div class="mb-1 flex items-center gap-2">
											<span class="truncate font-medium text-gray-900">
												{identity.display_name ||
													identity.platform_username ||
													identity.platform_user_id}
											</span>
											<span
												class="rounded-full px-2 py-0.5 text-xs {getPlatformColor(
													identity.platform
												)}"
											>
												{identity.platform}
											</span>
										</div>

										<!-- Customer Name -->
										<div class="truncate text-sm text-gray-600">
											{getCustomerName(identity)}
										</div>

										<!-- Latest Message Preview -->
										{#if latestMessages.has(identity.id)}
											{@const message = latestMessages.get(identity.id)}
											<div class="mt-1 truncate text-sm text-gray-500">
												{message.is_self ? 'You: ' : ''}{message.message}
											</div>
										{:else if identity.last_message}
											<div class="mt-1 truncate text-sm text-gray-500">
												{identity.last_message}
											</div>
										{/if}
									</div>

									<!-- Right Side Info -->
									<div class="ml-2 flex flex-col items-end">
										<!-- Time -->
										{#if latestMessages.has(identity.id)}
											{@const message = latestMessages.get(identity.id)}
											<span class="whitespace-nowrap text-xs text-gray-500">
												{formatTime(message.created_on)}
											</span>
										{:else if identity.last_message_time}
											<span class="whitespace-nowrap text-xs text-gray-500">
												{formatTime(identity.last_message_time)}
											</span>
										{/if}

										<!-- Unread Count -->
										{#if unreadCounts.get(identity.id) > 0}
											<span
												class="mt-1 inline-flex h-5 min-w-[20px] items-center justify-center rounded-full
														 bg-red-500 px-1.5 text-xs font-bold text-white"
											>
												{unreadCounts.get(identity.id)}
											</span>
										{:else if identity.unread_count > 0}
											<span
												class="mt-1 inline-flex h-5 min-w-[20px] items-center justify-center rounded-full
														 bg-red-500 px-1.5 text-xs font-bold text-white"
											>
												{identity.unread_count}
											</span>
										{/if}
									</div>
								</div>
							</button>
						{/each}
					</div>

					<!-- Load More -->
					{#if hasMore}
						<InfiniteScroll on:loadMore={handleLoadMore} loading={loadingMore} />
					{/if}
				{/if}
			</div>
		</TabItem>
		<TabItem title="Closed"></TabItem>
		<TabItem title="Open"></TabItem>
		<TabItem title="All"></TabItem>
	</Tabs>
</div>

<style>
	.custom-scrollbar {
		scrollbar-width: thin;
		scrollbar-color: #e5e7eb #f9fafb;
	}

	.custom-scrollbar::-webkit-scrollbar {
		width: 6px;
	}

	.custom-scrollbar::-webkit-scrollbar-track {
		background: #f9fafb;
	}

	.custom-scrollbar::-webkit-scrollbar-thumb {
		background-color: #e5e7eb;
		border-radius: 3px;
	}

	.custom-scrollbar::-webkit-scrollbar-thumb:hover {
		background-color: #d1d5db;
	}
</style>
