<script lang="ts">
	import { DotsVerticalOutline, CloseOutline, ClockSolid } from 'flowbite-svelte-icons';
	import { Button, Dropdown } from 'flowbite-svelte';
	import TransferTicketOwner from '$lib/components/UI/TransferTicketOwner.svelte';
	import ChangeTicketStatus from '$lib/components/UI/ChangeTicketStatus.svelte';
	import ChangeTicketPriority from '$lib/components/UI/ChangeTicketPriority.svelte';
	import { getInitials } from '$lib/utils/avatarGenerator';
	import { timeAgo } from '$lib/utils';

	export let customerId: number;
	export let customerName: string;
	export let channelName: string;
	export let connected: boolean = false;
	export let platformId: any = [];
	export let access_token: string;

	// export let ticket: any;
	export let users: any[];
	export let priorities: any[];
	export let statuses: any[];
	export let topics: any[];
	// console.log(topics)

	import { t } from '$lib/stores/i18n';
	import { services } from '$src/lib/api/features';
	import { invalidateAll } from '$app/navigation';

	// State variables
	let ticket: any = null;
	let loginUser: any = null;
	let loading = true;
	let error: string | null = null;
	let currentPriorityName: string = ''; // Add this reactive variable
	let dropdownOpen = false;

	// Async function to get ticket
	async function getTicket(customerId: number, platformId: any, accessToken: string) {
		try {
			loading = true;
			error = null;

			const platformInfo = await services.customers.getPlatformInfo(
				customerId,
				platformId,
				accessToken
			);
			const backendTicketId = platformInfo.id.toString();
			const response_ticket = await services.tickets.getById(backendTicketId, accessToken);
			const response_selfUserInfo = await services.users.getUserInfo(accessToken);

			ticket = response_ticket.tickets;
			loginUser = response_selfUserInfo.users;

			// Update the reactive variable
			currentPriorityName = ticket?.priority?.name || '';

			console.log('ticket priority = ' + ticket?.priority?.name);
			console.log('ticket status = ' + ticket?.status);

			// console.log('Ticket Information:', ticket)
			// console.log('Ticket Priority', currentPriorityName)
		} catch (err) {
			error = err instanceof Error ? err.message : 'Failed to fetch ticket';
			console.error('Error fetching ticket:', err);
			ticket = null; // Changed from [] to null for consistency
			loginUser = null;
			currentPriorityName = '';
		} finally {
			loading = false;
		}
	}

	// Enhanced badge configuration functions
	function getStatusBadgeConfig(status: string) {
		const configs: Record<string, { class: string; text: string; showIcon: boolean }> = {
			none: {
				class: '',
				text: '',
				showIcon: false
			},
			closed: {
				class: 'bg-gray-100 text-gray-700',
				text: 'Closed',
				showIcon: true
			},
			open: {
				class: 'bg-green-100 text-green-700',
				text: 'Open',
				showIcon: false
			},
			assigned: {
				class: 'bg-blue-100 text-blue-700',
				text: 'Assigned',
				showIcon: false
			},
			waiting: {
				class: 'bg-yellow-100 text-yellow-700',
				text: 'Waiting',
				showIcon: false
			},
			pending_to_close: {
				class: 'bg-orange-100 text-orange-700',
				text: 'Pending to Close',
				showIcon: false
			}
		};
		return configs[status?.toLowerCase()] || configs['none'];
	}

	function getPriorityBadgeConfig(priorityName: string) {
		const configs: Record<string, { class: string; text: string; showIcon: boolean }> = {
			none: {
				class: '',
				text: '',
				showIcon: false
			},
			Low: {
				class: 'bg-gray-100 text-gray-700',
				text: 'Low Priority',
				showIcon: false
			},
			Medium: {
				class: 'bg-yellow-100 text-yellow-700',
				text: 'Medium Priority',
				showIcon: true
			},
			High: {
				class: 'bg-orange-100 text-orange-700',
				text: 'High Priority',
				showIcon: true
			},
			Immediately: {
				class: 'bg-red-100 text-red-700',
				text: 'Immediate Priority',
				showIcon: true
			}
		};
		return configs[priorityName] || configs['none'];
	}

	// Reactive function to refetch when dependencies change
	$: if (customerId && platformId && access_token) {
		getTicket(customerId, platformId, access_token);
	}

	// Function to close dropdown
	function closeDropdown() {
		dropdownOpen = false;
	}

	// Reactive badge configurations
	$: statusBadgeConfig = getStatusBadgeConfig(ticket?.status);
	$: priorityBadgeConfig = getPriorityBadgeConfig(currentPriorityName);
</script>

<div class="border-b border-gray-200 bg-white px-4 py-4 sm:px-6">
	<div class="flex flex-col space-y-4 lg:flex-row lg:items-start lg:justify-between lg:space-y-0">
		<!-- Left: Avatar + Customer Info + Status Badges -->
		<div class="flex flex-col space-y-3">
			<!-- Avatar + Customer Info Row -->
			<div class="flex items-center space-x-3">
				<!-- Avatar -->
				<div class="flex-shrink-0">
					<div
						class="flex h-10 w-10 items-center justify-center rounded-full bg-blue-500 text-sm font-semibold text-white"
						role="img"
						aria-label="Customer avatar for {customerName}"
					>
						{getInitials(customerName)}
					</div>
				</div>
				<!-- Customer Info -->
				<div class="flex min-w-0 flex-col">
					<h2 class="truncate text-lg font-semibold text-gray-900">{customerName}</h2>
					<p class="truncate text-sm text-gray-500">{channelName}</p>
				</div>
			</div>
		</div>

		<!-- Right: Timestamp + Actions -->
		<div
			class="flex flex-col items-start justify-end space-y-2 sm:flex-row sm:items-center sm:space-x-4 sm:space-y-0"
		>
			<!-- Last Activity -->
			<!-- {#if ticket?.updated_at}
				<div class="flex items-center text-sm text-gray-500">
					<ClockSolid class="mr-1 h-4 w-4" aria-hidden="true" />
					<span class="whitespace-nowrap"
						>Last activity: {timeAgo(ticket.updated_at, ticket?.status || '')}</span
					>
				</div>
			{/if} -->

			<!-- Actions -->
			<div class="flex items-center justify-end gap-2">
				<Button
					color="none"
					class="rounded-lg p-2 hover:bg-gray-100 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
					aria-label="Open ticket actions menu"
				>
					<DotsVerticalOutline class="h-5 w-5 text-gray-600" aria-hidden="true" />
				</Button>
				<Dropdown class="w-48" placement="bottom-end" bind:open={dropdownOpen}>
					<TransferTicketOwner
						{ticket}
						{users}
						loggedInUsername={loginUser?.username}
						loggedInRole={loginUser?.roles?.[0]?.name}
						isDropDownItem={true}
						onSuccess={() => {
							getTicket(customerId, platformId, access_token);
							closeDropdown();
						}}
					/>
					<ChangeTicketStatus
						{ticket}
						{statuses}
						ticket_topics={topics}
						isDropDownItem={true}
						onSuccess={() => {
							getTicket(customerId, platformId, access_token);
							closeDropdown();
						}}
					/>
					<ChangeTicketPriority
						{ticket}
						{priorities}
						isDropDownItem={true}
						onSuccess={() => {
							getTicket(customerId, platformId, access_token);
							closeDropdown();
						}}
					/>
				</Dropdown>
			</div>
		</div>
	</div>

	<!-- Status Badges Row -->
	<div class="mt-3 flex w-full items-center justify-between">
		<!-- Left side badges group -->
		<div class="flex flex-wrap items-center gap-2 sm:gap-3">
			<!-- Status Badge -->
			<div
				class="flex items-center space-x-1 rounded px-3 py-1 {statusBadgeConfig.class}"
				role="status"
				aria-label="Ticket status: {statusBadgeConfig.text}"
			>
				<span class="whitespace-nowrap text-sm font-medium">
					{statusBadgeConfig.text}
				</span>
			</div>

			<!-- Priority Badge -->
			<div
				class="flex items-center space-x-1 rounded px-3 py-1 {priorityBadgeConfig.class}"
				role="status"
				aria-label="Ticket priority: {priorityBadgeConfig.text}"
			>
				<span class="whitespace-nowrap text-sm font-medium">
					{priorityBadgeConfig.text}
				</span>
			</div>
		</div>

		<!-- Connection Badge (right-aligned) -->
		<div
			class="flex items-center space-x-1 rounded px-3 py-1 {connected === true
				? 'bg-green-100 text-green-700'
				: connected === false
					? 'bg-red-100 text-red-700'
					: ''}"
			role="status"
			aria-label="Connection status: {connected ? t('connected') : t('disconnected')}"
		>
			<div
				class="h-2 w-2 rounded-full {connected ? 'bg-green-500' : 'bg-red-500'}"
				aria-hidden="true"
			></div>
			<span class="whitespace-nowrap text-sm font-medium">
				{connected === true ? t('connected') : connected === false ? t('disconnected') : ''}
			</span>
		</div>
	</div>
</div>
